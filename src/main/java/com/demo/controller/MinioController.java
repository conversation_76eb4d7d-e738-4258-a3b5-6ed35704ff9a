package com.demo.controller;


import cn.hutool.core.date.DateUtil;
import com.demo.utils.MinioUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/minio")
public class MinioController {

    @Autowired
    private MinioUtil minioUtil;

    /**
     * 上传文件
     */
    @PostMapping(value = "/upload")
    public String uploadReport(MultipartFile[] files) {
        ArrayList<String> urls = new ArrayList<>();
        for (MultipartFile file : files) {
            String fileName = file.getOriginalFilename();
            String formatDate = DateUtil.formatDate(new Date());    //日期
            String folderName = "审核人脸" + "/" + formatDate + "/" + "宜宾市高县庆符镇西江村西江村劝导站";    //构建文件夹名
            String s = minioUtil.uploadFile(file, fileName, folderName);
            urls.add(s);
        }
        return String.join(",", urls);
    }

    /**
     * 预览文件
     */
    @GetMapping("/preview")
    public String preview(String fileName) {
        return minioUtil.getFileUrl(fileName);
    }

    /**
     * 下载文件
     */
    @GetMapping("/download")
    public void download(String fileName, HttpServletResponse response) {
        minioUtil.download(response, fileName);
    }

    /**
     * 删除文件
     */
    @GetMapping("/delete")
    public String delete(String fileName) {
        minioUtil.delete(fileName);
        return "删除成功";
    }

    /**
     * 批量下载指定时间段和文件夹下的所有文件
     *
     * @param folderPath 文件夹路径（必需）
     * @param startTime 开始时间（必需，格式：yyyy-MM-dd HH:mm:ss）
     * @param endTime 结束时间（必需，格式：yyyy-MM-dd HH:mm:ss）
     * @param response HTTP响应对象
     */
    @GetMapping("/batchDownload")
    public void batchDownload(
            @RequestParam String folderPath,
            @RequestParam String startTime,
            @RequestParam String endTime,
            HttpServletResponse response) {

        try {
            // 参数验证
            if (folderPath == null || folderPath.trim().isEmpty()) {
                throw new IllegalArgumentException("文件夹路径不能为空");
            }
            if (startTime == null || startTime.trim().isEmpty()) {
                throw new IllegalArgumentException("开始时间不能为空");
            }
            if (endTime == null || endTime.trim().isEmpty()) {
                throw new IllegalArgumentException("结束时间不能为空");
            }

            // 时间格式转换
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime start = LocalDateTime.parse(startTime, formatter);
            LocalDateTime end = LocalDateTime.parse(endTime, formatter);

            // 验证时间范围
            if (start.isAfter(end)) {
                throw new IllegalArgumentException("开始时间不能晚于结束时间");
            }

            // 获取符合条件的文件列表
            List<String> fileList = minioUtil.listObjectsByTimeRange(folderPath, start, end);

            if (fileList.isEmpty()) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                response.getWriter().write("指定时间段和文件夹下没有找到任何文件");
                return;
            }

            // 生成ZIP文件名
            String zipFileName = String.format("batch_download_%s_%s_%s.zip",
                    folderPath.replace("/", "_"),
                    start.format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")),
                    end.format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));

            // 执行批量下载
            minioUtil.downloadMultipleFilesAsZip(fileList, response, zipFileName);

        } catch (IllegalArgumentException e) {
            try {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("参数错误: " + e.getMessage());
            } catch (Exception ignored) {}
        } catch (Exception e) {
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("批量下载失败: " + e.getMessage());
            } catch (Exception ignored) {}
            e.printStackTrace();
        }
    }
}



