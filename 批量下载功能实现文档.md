# MinIO 批量下载功能实现文档

## 功能概述

为MinioController新增了批量下载功能，支持按文件夹路径筛选文件，并将多个文件打包成ZIP格式下载。

## 新增功能特性

### 1. 智能路径匹配
- 支持指定文件夹路径
- **自动模糊匹配**：如`2025-08-0`自动匹配`2025-08-01`、`2025-08-02`等
- **智能判断**：以`/`结尾的路径使用精确匹配，否则使用模糊匹配
- 递归获取子文件夹中的所有文件

### 2. ZIP打包下载
- 将符合条件的多个文件打包成ZIP格式
- 采用扁平化文件结构，避免文件名冲突
- 文件命名规则：将路径分隔符`/`替换为下划线`_`

## 代码实现

### 1. MinioUtil 新增方法

#### listObjectsByTimeRange()
```java
/**
 * 获取指定文件夹下的所有文件列表（智能模糊匹配，不限制时间）
 *
 * @param folderPath 文件夹路径（自动支持模糊匹配）
 * @return 符合条件的文件列表
 */
public List<String> listObjectsByTimeRange(String folderPath)
```

**功能说明：**
- 使用MinIO的`listObjects()`方法递归获取文件
- **智能匹配策略**：
  - 路径以`/`结尾 → 精确匹配（如`审核人脸/2024-01-15/`）
  - 路径不以`/`结尾 → 模糊匹配（如`2025-08-0`匹配`2025-08-01`、`2025-08-02`等）
- 获取所有文件，不限制时间范围
- 自动跳过文件夹对象（以`/`结尾的对象）

#### downloadMultipleFilesAsZip()
```java
/**
 * 批量下载文件并打包为ZIP（扁平化结构）
 *
 * @param fileList 文件列表
 * @param response HTTP响应对象
 * @param zipFileName ZIP文件名
 */
public void downloadMultipleFilesAsZip(List<String> fileList, HttpServletResponse response, String zipFileName)
```

**功能说明：**
- 创建ZIP输出流直接写入HTTP响应
- 扁平化文件名：`folder1/subfolder1/image1.jpg` → `folder1_subfolder1_image1.jpg`
- 异常处理：单个文件失败不影响其他文件处理

### 2. MinioController 新增接口

#### /minio/batchDownload
```java
@GetMapping("/batchDownload")
public void batchDownload(
    @RequestParam String folderPath,
    HttpServletResponse response)
```

**请求参数：**
- `folderPath`: 文件夹路径（必需，自动智能匹配）

**响应：**
- 成功：返回ZIP文件流
- 失败：返回错误信息和相应的HTTP状态码

## 使用示例

### 请求示例

#### 1. 精确路径匹配（以/结尾）
```
GET /minio/batchDownload?folderPath=审核人脸/2024-01-15/
```

#### 2. 智能模糊匹配（不以/结尾）
```
# 自动匹配所有以"2025-08-0"开头的文件夹（如2025-08-01, 2025-08-02等）
GET /minio/batchDownload?folderPath=2025-08-0

# 自动匹配审核人脸文件夹下所有2024年1月的文件
GET /minio/batchDownload?folderPath=审核人脸/2024-01

# 匹配所有包含"审核人脸"的路径
GET /minio/batchDownload?folderPath=审核人脸
```

### 文件结构示例

**原始MinIO结构：**
```
审核人脸/2024-01-15/宜宾市高县庆符镇西江村西江村劝导站/
├── image1.jpg
├── image2.jpg
└── subfolder/
    ├── image3.jpg
    └── image4.jpg
```

**下载ZIP内部结构（扁平化）：**
```
batch_download_审核人脸_2024-01-15.zip
├── 审核人脸_2024-01-15_宜宾市高县庆符镇西江村西江村劝导站_image1.jpg
├── 审核人脸_2024-01-15_宜宾市高县庆符镇西江村西江村劝导站_image2.jpg
├── 审核人脸_2024-01-15_宜宾市高县庆符镇西江村西江村劝导站_subfolder_image3.jpg
└── 审核人脸_2024-01-15_宜宾市高县庆符镇西江村西江村劝导站_subfolder_image4.jpg
```

## 智能匹配功能详解

### 1. 自动匹配策略
- **精确匹配**：路径以`/`结尾时使用精确匹配
  - `folderPath="审核人脸/2024-01-15/"` → 只匹配该确切路径
- **模糊匹配**：路径不以`/`结尾时自动启用模糊匹配
  - `folderPath="2025-08-0"` → 匹配 `2025-08-01/`, `2025-08-02/`, `2025-08-03/` 等
  - `folderPath="审核人脸/2024"` → 匹配 `审核人脸/2024-01/`, `审核人脸/2024-02/` 等

### 2. 匹配逻辑
1. 首先按时间范围筛选文件
2. 然后按路径匹配策略筛选
3. 返回符合所有条件的文件列表

### 3. 性能优化
- 智能选择MinIO查询前缀，减少网络传输
- 使用最短有效前缀进行初始查询
- 在客户端进行精确的二次筛选

## 错误处理

### 参数验证
- 文件夹路径不能为空
- 开始时间和结束时间不能为空
- 开始时间不能晚于结束时间
- 时间格式必须符合：`yyyy-MM-dd HH:mm:ss`

### 异常情况
- **400 Bad Request**: 参数错误
- **404 Not Found**: 指定条件下没有找到文件
- **500 Internal Server Error**: 服务器内部错误

### 容错机制
- 单个文件下载失败不影响其他文件
- 自动跳过文件夹对象
- 详细的错误日志记录

## 性能考虑

### 内存优化
- 使用流式处理，避免将所有文件加载到内存
- ZIP文件直接写入HTTP响应流
- 及时关闭文件输入流

### 网络优化
- 支持大文件批量下载
- 客户端连接中断时自动停止处理

## 技术依赖

### 新增导入
```java
import io.minio.messages.Item;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
```

### 现有依赖
- MinIO Java SDK 8.3.4
- Spring Boot Web
- Apache Commons IO（通过Tomcat提供）

## 注意事项

1. **时区处理**: 使用系统默认时区进行时间转换
2. **文件名编码**: 使用UTF-8编码处理中文文件名
3. **路径处理**: 自动处理文件夹路径格式（添加/移除末尾斜杠）
4. **扁平化命名**: 避免文件名冲突，但可能导致文件名较长

## 后续优化建议

1. **分页支持**: 对于大量文件，可考虑添加分页参数
2. **压缩级别**: 允许用户选择ZIP压缩级别
3. **进度反馈**: 对于大文件批量下载，提供进度信息
4. **异步处理**: 对于超大批量下载，可考虑异步处理并提供下载链接
5. **正则表达式支持**: 支持更复杂的路径匹配模式
6. **缓存机制**: 对频繁查询的文件列表进行缓存
